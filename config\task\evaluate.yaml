datasets_root: ${hydra:runtime.cwd}/datasets
output_root: ${hydra:runtime.cwd}/output
root: ${hydra:runtime.cwd}

# Evaluation parameters
instance_mode: False
iou_threshold: 0.5
use_chunks: True
num_chunks: 8
n_jobs: -1
use_gpu: False
use_specific_format: False
use_datasets_info: False

# Volume size mismatch handling
size_mismatch_mode: "scale"  # Options: "scale" or "crop"
                            # "scale": Resize prediction to match ground truth size
                            # "crop": Crop prediction to match ground truth size from [0][0][0]

# Paths
path_info:
  train:
    root_dir: "${datasets_root}/finetune"
    mask_dir_name: sdf
    ref_mask_dir_name: sdfu
    normalization_file: "${datasets_root}/finetune/normalization_params.json"
    datasets_info:
      - name: jrc_hela-1
        organelles:
          - em: endo
            seg: endo
          - em: mito
            seg: mito

      - name: jrc_hela-2
        organelles:
          - em: endo_lyso
            seg: endo
          - em: endo_lyso
            seg: lyso

      - name: jrc_hela-3
        organelles:
          - em: endo
            seg: endo
          - em: lyso
            seg: lyso

      - name: jrc_jurkat-1
        organelles:
          - em: mito
            seg: mito
          - em: chrom
            seg: chrom

      - name: jrc_macrophage-2
        organelles:
          - em: endo_lyso
            seg: endo
          - em: endo_lyso
            seg: lyso

      - name: jrc_mus-heart-1
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-liver-3
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-liver-4
        organelles:
          - em: mito_ld
            seg: mito
          - em: mito_ld
            seg: ld

      - name: jrc_mus-liver-5
        organelles:
          - em: ld
            seg: ld
          - em: mito
            seg: mito

      - name: jrc_mus-liver-7
        organelles:
          - em: mito_ld
            seg: ld
          - em: mito_ld
            seg: mito

      - name: jrc_mus-meissner-corpuscle-1
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-pancreas-4
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-skin-1
        organelles:
          - em: nuc
            seg: nuc
  val:
    root_dir: "${datasets_root}/finetune"
    normalization_file: "${datasets_root}/finetune/normalization_params.json"
    mask_dir_name: sdf
    ref_mask_dir_name: sdfu
    datasets_info:
      - name: jrc_hela-1
        organelles:
          - em: endo
            seg: endo
          - em: mito
            seg: mito

      - name: jrc_hela-2
        organelles:
          - em: endo_lyso
            seg: endo
          - em: endo_lyso
            seg: lyso

      - name: jrc_hela-3
        organelles:
          - em: endo
            seg: endo
          - em: lyso
            seg: lyso

      - name: jrc_jurkat-1
        organelles:
          - em: mito
            seg: mito
          - em: chrom
            seg: chrom

      - name: jrc_macrophage-2
        organelles:
          - em: endo_lyso
            seg: endo
          - em: endo_lyso
            seg: lyso

      - name: jrc_mus-heart-1
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-liver-3
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-liver-4
        organelles:
          - em: mito_ld
            seg: mito
          - em: mito_ld
            seg: ld

      - name: jrc_mus-liver-5
        organelles:
          - em: ld
            seg: ld
          - em: mito
            seg: mito

      - name: jrc_mus-liver-7
        organelles:
          - em: mito_ld
            seg: ld
          - em: mito_ld
            seg: mito

      - name: jrc_mus-meissner-corpuscle-1
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-pancreas-4
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-skin-1
        organelles:
          - em: nuc
            seg: nuc
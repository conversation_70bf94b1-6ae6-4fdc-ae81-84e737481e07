volume_dir: "${datasets_root}/em_s0/single"
masks_dir: "${datasets_root}/mito_seg/single"
sam2_config_path: "sam2_config/sam2.1_hiera_t.yaml"
sam2_model_path: "${root}/SAM2/checkpoints/sam2.1_hiera_tiny.pt"
output_dir: "${output_root}/test/new"

state: "fewshot"
keep_in_mem: true
save_masks: true
mask_to_binary: false
label_start: 400
label_stride: 200
resolution: 1024
to_sdf: true
unet_downsample: 2
data_loader: "DatasetDataLoader"

path_config:
  split:
    - "train"
    - "val"
  output_dir_name: sdf_avg
  train:
    root_dir: "${datasets_root}/finetune"
    normalization_file: "${datasets_root}/finetune/normalization_params.json"
    datasets_info:
      - name: jrc_hela-1
        organelles:
          - em: endo
            seg: endo
          - em: mito
            seg: mito

      - name: jrc_hela-2
        organelles:
          - em: endo_lyso
            seg: endo
          - em: endo_lyso
            seg: lyso

      - name: jrc_hela-3
        organelles:
          - em: endo
            seg: endo
          - em: lyso
            seg: lyso

      - name: jrc_jurkat-1
        organelles:
          - em: mito
            seg: mito
          - em: chrom
            seg: chrom

      - name: jrc_macrophage-2
        organelles:
          - em: endo_lyso
            seg: endo
          - em: endo_lyso
            seg: lyso

      - name: jrc_mus-heart-1
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-liver-3
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-liver-4
        organelles:
          - em: mito_ld
            seg: mito
          - em: mito_ld
            seg: ld

      - name: jrc_mus-liver-5
        organelles:
          - em: ld
            seg: ld
          - em: mito
            seg: mito

      - name: jrc_mus-liver-7
        organelles:
          - em: mito_ld
            seg: ld
          - em: mito_ld
            seg: mito

      - name: jrc_mus-meissner-corpuscle-1
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-pancreas-4
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-skin-1
        organelles:
          - em: nuc
            seg: nuc
  val:
    root_dir: "${datasets_root}/finetune"
    normalization_file: "${datasets_root}/finetune/normalization_params.json"
    datasets_info:
      # - name: jrc_hela-1
      #   organelles:
      #     - em: endo
      #       seg: endo
      #     - em: mito
      #       seg: mito

      # - name: jrc_hela-2
      #   organelles:
      #     - em: endo_lyso
      #       seg: endo
      #     - em: endo_lyso
      #       seg: lyso

      # - name: jrc_hela-3
      #   organelles:
      #     - em: endo
      #       seg: endo
      #     - em: lyso
      #       seg: lyso

      # - name: jrc_jurkat-1
      #   organelles:
      #     - em: mito
      #       seg: mito
      #     - em: chrom
      #       seg: chrom

      # - name: jrc_macrophage-2
      #   organelles:
      #     - em: endo_lyso
      #       seg: endo
      #     - em: endo_lyso
      #       seg: lyso

      - name: jrc_mus-heart-1
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-liver-3
        organelles:
          - em: nuc
            seg: nuc

      # - name: jrc_mus-liver-4
      #   organelles:
      #     - em: mito_ld
      #       seg: mito
      #     - em: mito_ld
      #       seg: ld

      - name: jrc_mus-liver-5
        organelles:
          - em: ld
            seg: ld
          - em: mito
            seg: mito

      - name: jrc_mus-liver-7
        organelles:
          - em: mito_ld
            seg: ld
          - em: mito_ld
            seg: mito

      - name: jrc_mus-meissner-corpuscle-1
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-pancreas-4
        organelles:
          - em: nuc
            seg: nuc

      - name: jrc_mus-skin-1
        organelles:
          - em: nuc
            seg: nuc